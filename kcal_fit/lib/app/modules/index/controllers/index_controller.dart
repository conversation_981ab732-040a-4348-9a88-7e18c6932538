import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:health/health.dart';

import '../../../../core/utils/logger_util.dart';
import '../../../shared/controllers/base_controller.dart';
import '../../../data/models/diet_analysis.dart';
import '../../../../core/utils/date_picker_util.dart';
import '../../../data/services/sqflite/dao/impl/diet_analysis_dao.dart';
import '../../../data/services/hive/hive_service.dart';
import '../widgets/calories_detail_widget.dart';

/// 首页控制器
/// 负责管理首页的饮食数据展示和日期选择功能
class IndexController extends BaseController {
  /// 当前查看的数据日期，默认为今天
  final RxString _dataDate = DateTime.now().toString().split(" ")[0].obs;

  /// 饮食分析数据访问对象
  final DietAnalysisDao _dietAnalysisDao = DietAnalysisDao();

  /// 有数据的日期列表
  final RxList<DateTime> _analysisDays = <DateTime>[].obs;

  /// 用户昵称
  final RxString _userName = '用户'.obs;

  /// 最后更新时间
  final RxString _lastUpdateTime = ''.obs;

  /// 今日健康数据（消耗）
  final RxDouble _todayActiveCalories = 0.0.obs; // 活动消耗卡路里
  final RxDouble _todayBasalCalories = 0.0.obs; // 基础代谢卡路里
  final RxDouble _todayTotalBurnedCalories = 0.0.obs; // 总消耗卡路里
  final RxInt _todaySteps = 0.obs; // 步数
  final RxDouble _todayDistance = 0.0.obs; // 距离（公里）
  final RxInt _todayExerciseTime = 0.obs; // 运动时间（分钟）

  /// 详细健康数据列表（用于详情页面）
  final RxList<HealthDataPoint> _detailedStepsData = <HealthDataPoint>[].obs;
  final RxList<HealthDataPoint> _detailedActiveCaloriesData = <HealthDataPoint>[].obs;
  final RxList<HealthDataPoint> _detailedBasalCaloriesData = <HealthDataPoint>[].obs;

  /// 今日营养素摄入数据
  final RxInt _todayCalories = 0.obs; // 卡路里
  final RxInt _todayProteinSum = 0.obs; // 蛋白质
  final RxInt _todayCarbsSum = 0.obs; // 碳水化合物
  final RxInt _todayFatSum = 0.obs; // 脂肪
  final RxInt _todayFiberSum = 0.obs; // 膳食纤维
  final RxInt _todaySugarSum = 0.obs; // 糖分
  final RxInt _todaySodiumSum = 0.obs; // 钠
  final RxInt _todayCholesterolSum = 0.obs; // 胆固醇

  // 维生素
  final RxInt _todayVitaminASum = 0.obs; // 维生素A
  final RxInt _todayThiamineSum = 0.obs; // 维生素B1
  final RxInt _todayRiboflavinSum = 0.obs; // 维生素B2
  final RxInt _todayNiacinSum = 0.obs; // 维生素B3
  final RxInt _todayPyridoxineSum = 0.obs; // 维生素B6
  final RxInt _todayCobalaminSum = 0.obs; // 维生素B12
  final RxInt _todayFolateSum = 0.obs; // 叶酸
  final RxInt _todayVitaminCSum = 0.obs; // 维生素C
  final RxInt _todayVitaminDSum = 0.obs; // 维生素D
  final RxInt _todayVitaminESum = 0.obs; // 维生素E
  final RxInt _todayVitaminKSum = 0.obs; // 维生素K

  // 矿物质
  final RxInt _todayCalciumSum = 0.obs; // 钙
  final RxInt _todayIronSum = 0.obs; // 铁
  final RxInt _todayMagnesiumSum = 0.obs; // 镁
  final RxInt _todayPhosphorusSum = 0.obs; // 磷
  final RxInt _todayPotassiumSum = 0.obs; // 钾
  final RxInt _todayZincSum = 0.obs; // 锌
  final RxInt _todayCopperSum = 0.obs; // 铜
  final RxInt _todayManganeseSum = 0.obs; // 锰
  final RxInt _todaySeleniumSum = 0.obs; // 硒
  final RxInt _todayIodineSum = 0.obs; // 碘
  final RxInt _todayChromiumSum = 0.obs; // 铬
  final RxInt _todayMolybdenumSum = 0.obs; // 钼

  // 脂肪酸
  final RxInt _todayOmega3Sum = 0.obs; // Omega-3
  final RxInt _todayOmega6Sum = 0.obs; // Omega-6
  final RxInt _todayTransFatSum = 0.obs; // 反式脂肪
  final RxInt _todaySaturatedFatSum = 0.obs; // 饱和脂肪
  final RxInt _todayMonounsaturatedFatSum = 0.obs; // 单不饱和脂肪
  final RxInt _todayPolyunsaturatedFatSum = 0.obs; // 多不饱和脂肪

  // Getter方法，提供对私有变量的只读访问
  String get dataDate => _dataDate.value;
  String get userName => _userName.value;
  String get lastUpdateTime => _lastUpdateTime.value;
  List<DateTime> get analysisDays => _analysisDays;

  // 健康数据getter
  double get todayActiveCalories => _todayActiveCalories.value;
  double get todayBasalCalories => _todayBasalCalories.value;
  double get todayTotalBurnedCalories => _todayTotalBurnedCalories.value;
  int get todaySteps => _todaySteps.value;
  double get todayDistance => _todayDistance.value;
  int get todayExerciseTime => _todayExerciseTime.value;
  List<HealthDataPoint> get detailedStepsData => _detailedStepsData;
  List<HealthDataPoint> get detailedActiveCaloriesData => _detailedActiveCaloriesData;
  List<HealthDataPoint> get detailedBasalCaloriesData => _detailedBasalCaloriesData;

  // 营养摄入数据getter
  int get todayCalories => _todayCalories.value;
  int get todayProteinSum => _todayProteinSum.value;
  int get todayCarbsSum => _todayCarbsSum.value;
  int get todayFatSum => _todayFatSum.value;
  int get todayFiberSum => _todayFiberSum.value;
  int get todaySugarSum => _todaySugarSum.value;
  int get todaySodiumSum => _todaySodiumSum.value;
  int get todayCholesterolSum => _todayCholesterolSum.value;

  // 维生素 getter
  int get todayVitaminASum => _todayVitaminASum.value;
  int get todayThiamineSum => _todayThiamineSum.value;
  int get todayRiboflavinSum => _todayRiboflavinSum.value;
  int get todayNiacinSum => _todayNiacinSum.value;
  int get todayPyridoxineSum => _todayPyridoxineSum.value;
  int get todayCobalaminSum => _todayCobalaminSum.value;
  int get todayFolateSum => _todayFolateSum.value;
  int get todayVitaminCSum => _todayVitaminCSum.value;
  int get todayVitaminDSum => _todayVitaminDSum.value;
  int get todayVitaminESum => _todayVitaminESum.value;
  int get todayVitaminKSum => _todayVitaminKSum.value;

  // 矿物质 getter
  int get todayCalciumSum => _todayCalciumSum.value;
  int get todayIronSum => _todayIronSum.value;
  int get todayMagnesiumSum => _todayMagnesiumSum.value;
  int get todayPhosphorusSum => _todayPhosphorusSum.value;
  int get todayPotassiumSum => _todayPotassiumSum.value;
  int get todayZincSum => _todayZincSum.value;
  int get todayCopperSum => _todayCopperSum.value;
  int get todayManganeseSum => _todayManganeseSum.value;
  int get todaySeleniumSum => _todaySeleniumSum.value;
  int get todayIodineSum => _todayIodineSum.value;
  int get todayChromiumSum => _todayChromiumSum.value;
  int get todayMolybdenumSum => _todayMolybdenumSum.value;

  // 脂肪酸 getter
  int get todayOmega3Sum => _todayOmega3Sum.value;
  int get todayOmega6Sum => _todayOmega6Sum.value;
  int get todayTransFatSum => _todayTransFatSum.value;
  int get todaySaturatedFatSum => _todaySaturatedFatSum.value;
  int get todayMonounsaturatedFatSum => _todayMonounsaturatedFatSum.value;
  int get todayPolyunsaturatedFatSum => _todayPolyunsaturatedFatSum.value;

  @override
  void onInit() {
    super.onInit();
    // 异步加载用户名，避免阻塞初始化
    Future.microtask(() => _loadUserName());
    // 延迟加载今日数据，优先显示UI
    Future.delayed(const Duration(milliseconds: 100), () {
      refreshTodayData();
      getTodayHealthData(dataDate);
      _updateLastUpdateTime();
    });
  }

  @override
  Future<void> onRefresh() async {
    await refreshTodayData();
    await getTodayHealthData(dataDate);
    _updateLastUpdateTime();
  }

  /// 从Hive加载用户昵称
  Future<void> _loadUserName() async {
    try {
      final nickname = await HiveService.getData<String>("user", "nickname");
      if (nickname != null && nickname.isNotEmpty) {
        _userName.value = nickname;
        LoggerUtil.i('用户昵称加载成功: $nickname');
      } else {
        _userName.value = '用户';
        LoggerUtil.i('未找到用户昵称，使用默认值');
      }
    } catch (e) {
      _userName.value = '用户';
      LoggerUtil.e('加载用户昵称失败', e);
    }
  }

  /// 获取有数据的日期列表
  /// 从数据库中查询所有有饮食记录的日期
  Future<void> getAnalysisDays() async {
    await executeAsync(() async {
      final data = await _dietAnalysisDao.getAnalysisDays();
      _analysisDays.clear();

      for (var dateRecord in data) {
        final dateTime = DateTime.parse(dateRecord['meal_time'].toString());
        _analysisDays.add(dateTime);
      }

      LoggerUtil.i("获取到有数据的日期数量: ${_analysisDays.length}");
    });
  }

  /// 底部导航切换到页面时执行
  /// 刷新当前页面的数据
  void onTabSelected() {
    LoggerUtil.d("数据日期: $dataDate");
    // 只有在数据为空时才刷新，避免重复加载
    if (_todayCalories.value == 0) {
      refreshTodayData();
      getTodayHealthData(dataDate);
    }
  }

  /// 刷新今日数据
  /// 重新获取当天的饮食分析数据
  Future<void> refreshTodayData() async {
    LoggerUtil.d("refreshTodayData - 刷新数据");
    await getTodayAnalysis();
  }

  /// 获取当天饮食分析数据
  /// 查询指定日期的所有饮食分析记录，并计算营养素总和
  Future<void> getTodayAnalysis() async {
    await executeAsync(() async {
      LoggerUtil.i("开始查询饮食分析数据 - 日期: $dataDate");

      // 查询当天的所有饮食分析记录
      final analysisList = await _dietAnalysisDao.getTodayAnalysis(dataDate);

      LoggerUtil.i("查询到的饮食分析记录数量: ${analysisList.length}");

      // 如果没有记录，重置所有数值为0
      if (analysisList.isEmpty) {
        LoggerUtil.i("没有找到饮食记录，重置营养素数值");
        _resetNutritionValues();
        setEmpty(true);
        return;
      }

      setEmpty(false);

      // 计算营养素总和
      final nutritionSummary = _calculateNutritionSummary(analysisList);

      // 更新UI显示的数值
      _updateNutritionDisplay(nutritionSummary);

      LoggerUtil.i("当天营养素汇总完成: $nutritionSummary");
    });
  }

  /// 重置营养素数值为0
  void _resetNutritionValues() {
    // 基础营养素
    _todayCalories.value = 0;
    _todayProteinSum.value = 0;
    _todayCarbsSum.value = 0;
    _todayFatSum.value = 0;
    _todayFiberSum.value = 0;
    _todaySugarSum.value = 0;
    _todaySodiumSum.value = 0;
    _todayCholesterolSum.value = 0;

    // 维生素
    _todayVitaminASum.value = 0;
    _todayThiamineSum.value = 0;
    _todayRiboflavinSum.value = 0;
    _todayNiacinSum.value = 0;
    _todayPyridoxineSum.value = 0;
    _todayCobalaminSum.value = 0;
    _todayFolateSum.value = 0;
    _todayVitaminCSum.value = 0;
    _todayVitaminDSum.value = 0;
    _todayVitaminESum.value = 0;
    _todayVitaminKSum.value = 0;

    // 矿物质
    _todayCalciumSum.value = 0;
    _todayIronSum.value = 0;
    _todayMagnesiumSum.value = 0;
    _todayPhosphorusSum.value = 0;
    _todayPotassiumSum.value = 0;
    _todayZincSum.value = 0;
    _todayCopperSum.value = 0;
    _todayManganeseSum.value = 0;
    _todaySeleniumSum.value = 0;
    _todayIodineSum.value = 0;
    _todayChromiumSum.value = 0;
    _todayMolybdenumSum.value = 0;

    // 脂肪酸
    _todayOmega3Sum.value = 0;
    _todayOmega6Sum.value = 0;
    _todayTransFatSum.value = 0;
    _todaySaturatedFatSum.value = 0;
    _todayMonounsaturatedFatSum.value = 0;
    _todayPolyunsaturatedFatSum.value = 0;
  }

  /// 计算营养素总和
  ///
  /// [analysisList] 饮食分析记录列表
  /// 返回营养素汇总数据
  Map<String, int> _calculateNutritionSummary(List<DietAnalysis> analysisList) {
    // 基础营养素
    int totalCalories = 0;
    int totalProtein = 0;
    int totalCarbs = 0;
    int totalFat = 0;
    int totalFiber = 0;
    int totalSugar = 0;
    int totalSodium = 0;
    int totalCholesterol = 0;

    // 维生素
    int totalVitaminA = 0;
    int totalThiamine = 0;
    int totalRiboflavin = 0;
    int totalNiacin = 0;
    int totalPyridoxine = 0;
    int totalCobalamin = 0;
    int totalFolate = 0;
    int totalVitaminC = 0;
    int totalVitaminD = 0;
    int totalVitaminE = 0;
    int totalVitaminK = 0;

    // 矿物质
    int totalCalcium = 0;
    int totalIron = 0;
    int totalMagnesium = 0;
    int totalPhosphorus = 0;
    int totalPotassium = 0;
    int totalZinc = 0;
    int totalCopper = 0;
    int totalManganese = 0;
    int totalSelenium = 0;
    int totalIodine = 0;
    int totalChromium = 0;
    int totalMolybdenum = 0;

    // 脂肪酸
    int totalOmega3 = 0;
    int totalOmega6 = 0;
    int totalTransFat = 0;
    int totalSaturatedFat = 0;
    int totalMonounsaturatedFat = 0;
    int totalPolyunsaturatedFat = 0;

    for (var analysis in analysisList) {
      // 累加基础营养素
      totalCalories += analysis.calories ?? 0;
      totalProtein += analysis.protein ?? 0;
      totalCarbs += analysis.carbs ?? 0;
      totalFat += analysis.fat ?? 0;
      totalFiber += analysis.fiber ?? 0;
      totalSugar += analysis.sugar ?? 0;
      totalSodium += analysis.sodium ?? 0;
      totalCholesterol += analysis.cholesterol ?? 0;

      // 累加维生素
      totalVitaminA += analysis.vitaminA ?? 0;
      totalThiamine += analysis.thiamine ?? 0;
      totalRiboflavin += analysis.riboflavin ?? 0;
      totalNiacin += analysis.niacin ?? 0;
      totalPyridoxine += analysis.pyridoxine ?? 0;
      totalCobalamin += analysis.cobalamin ?? 0;
      totalFolate += analysis.folate ?? 0;
      totalVitaminC += analysis.vitaminC ?? 0;
      totalVitaminD += analysis.vitaminD ?? 0;
      totalVitaminE += analysis.vitaminE ?? 0;
      totalVitaminK += analysis.vitaminK ?? 0;

      // 累加矿物质
      totalCalcium += analysis.calcium ?? 0;
      totalIron += analysis.iron ?? 0;
      totalMagnesium += analysis.magnesium ?? 0;
      totalPhosphorus += analysis.phosphorus ?? 0;
      totalPotassium += analysis.potassium ?? 0;
      totalZinc += analysis.zinc ?? 0;
      totalCopper += analysis.copper ?? 0;
      totalManganese += analysis.manganese ?? 0;
      totalSelenium += analysis.selenium ?? 0;
      totalIodine += analysis.iodine ?? 0;
      totalChromium += analysis.chromium ?? 0;
      totalMolybdenum += analysis.molybdenum ?? 0;

      // 累加脂肪酸
      totalOmega3 += analysis.omega3 ?? 0;
      totalOmega6 += analysis.omega6 ?? 0;
      totalTransFat += analysis.transFat ?? 0;
      totalSaturatedFat += analysis.saturatedFat ?? 0;
      totalMonounsaturatedFat += analysis.monounsaturatedFat ?? 0;
      totalPolyunsaturatedFat += analysis.polyunsaturatedFat ?? 0;

      LoggerUtil.i(
        "分析记录 ${analysis.analyzeId}: "
        "卡路里=${analysis.calories}, "
        "蛋白质=${analysis.protein}, "
        "碳水=${analysis.carbs}, "
        "脂肪=${analysis.fat}, "
        "维生素C=${analysis.vitaminC}, "
        "钙=${analysis.calcium}",
      );
    }

    return {
      // 基础营养素
      'calories': totalCalories,
      'protein': totalProtein,
      'carbs': totalCarbs,
      'fat': totalFat,
      'fiber': totalFiber,
      'sugar': totalSugar,
      'sodium': totalSodium,
      'cholesterol': totalCholesterol,

      // 维生素
      'vitaminA': totalVitaminA,
      'thiamine': totalThiamine,
      'riboflavin': totalRiboflavin,
      'niacin': totalNiacin,
      'pyridoxine': totalPyridoxine,
      'cobalamin': totalCobalamin,
      'folate': totalFolate,
      'vitaminC': totalVitaminC,
      'vitaminD': totalVitaminD,
      'vitaminE': totalVitaminE,
      'vitaminK': totalVitaminK,

      // 矿物质
      'calcium': totalCalcium,
      'iron': totalIron,
      'magnesium': totalMagnesium,
      'phosphorus': totalPhosphorus,
      'potassium': totalPotassium,
      'zinc': totalZinc,
      'copper': totalCopper,
      'manganese': totalManganese,
      'selenium': totalSelenium,
      'iodine': totalIodine,
      'chromium': totalChromium,
      'molybdenum': totalMolybdenum,

      // 脂肪酸
      'omega3': totalOmega3,
      'omega6': totalOmega6,
      'transFat': totalTransFat,
      'saturatedFat': totalSaturatedFat,
      'monounsaturatedFat': totalMonounsaturatedFat,
      'polyunsaturatedFat': totalPolyunsaturatedFat,
    };
  }

  /// 更新营养素显示数值
  /// [summary] 营养素汇总数据
  void _updateNutritionDisplay(Map<String, int> summary) {
    LoggerUtil.i("更新营养素显示数值: $summary");

    final oldCalories = _todayCalories.value;

    // 更新基础营养素
    _todayCalories.value = summary['calories'] ?? 0;
    _todayProteinSum.value = summary['protein'] ?? 0;
    _todayCarbsSum.value = summary['carbs'] ?? 0;
    _todayFatSum.value = summary['fat'] ?? 0;
    _todayFiberSum.value = summary['fiber'] ?? 0;
    _todaySugarSum.value = summary['sugar'] ?? 0;
    _todaySodiumSum.value = summary['sodium'] ?? 0;
    _todayCholesterolSum.value = summary['cholesterol'] ?? 0;

    // 更新维生素
    _todayVitaminASum.value = summary['vitaminA'] ?? 0;
    _todayThiamineSum.value = summary['thiamine'] ?? 0;
    _todayRiboflavinSum.value = summary['riboflavin'] ?? 0;
    _todayNiacinSum.value = summary['niacin'] ?? 0;
    _todayPyridoxineSum.value = summary['pyridoxine'] ?? 0;
    _todayCobalaminSum.value = summary['cobalamin'] ?? 0;
    _todayFolateSum.value = summary['folate'] ?? 0;
    _todayVitaminCSum.value = summary['vitaminC'] ?? 0;
    _todayVitaminDSum.value = summary['vitaminD'] ?? 0;
    _todayVitaminESum.value = summary['vitaminE'] ?? 0;
    _todayVitaminKSum.value = summary['vitaminK'] ?? 0;

    // 更新矿物质
    _todayCalciumSum.value = summary['calcium'] ?? 0;
    _todayIronSum.value = summary['iron'] ?? 0;
    _todayMagnesiumSum.value = summary['magnesium'] ?? 0;
    _todayPhosphorusSum.value = summary['phosphorus'] ?? 0;
    _todayPotassiumSum.value = summary['potassium'] ?? 0;
    _todayZincSum.value = summary['zinc'] ?? 0;
    _todayCopperSum.value = summary['copper'] ?? 0;
    _todayManganeseSum.value = summary['manganese'] ?? 0;
    _todaySeleniumSum.value = summary['selenium'] ?? 0;
    _todayIodineSum.value = summary['iodine'] ?? 0;
    _todayChromiumSum.value = summary['chromium'] ?? 0;
    _todayMolybdenumSum.value = summary['molybdenum'] ?? 0;

    // 更新脂肪酸
    _todayOmega3Sum.value = summary['omega3'] ?? 0;
    _todayOmega6Sum.value = summary['omega6'] ?? 0;
    _todayTransFatSum.value = summary['transFat'] ?? 0;
    _todaySaturatedFatSum.value = summary['saturatedFat'] ?? 0;
    _todayMonounsaturatedFatSum.value = summary['monounsaturatedFat'] ?? 0;
    _todayPolyunsaturatedFatSum.value = summary['polyunsaturatedFat'] ?? 0;

    LoggerUtil.i("卡路里更新: $oldCalories -> ${_todayCalories.value}");
    LoggerUtil.i("蛋白质: ${_todayProteinSum.value}, 碳水: ${_todayCarbsSum.value}, 脂肪: ${_todayFatSum.value}");
    LoggerUtil.i("维生素C: ${_todayVitaminCSum.value}, 钙: ${_todayCalciumSum.value}, 铁: ${_todayIronSum.value}");
  }

  /// 选择日期
  /// 显示日期选择器，并标记有数据的日期
  Future<void> selectDate(BuildContext context) async {
    await executeAsync(() async {
      // 先获取有数据的日期
      await getAnalysisDays();

      LoggerUtil.i("有数据的日期: $analysisDays");

      // 调用带标记的日期选择器
      final selectedDate = await DatePickerUtil.pickDateWithMarks(
        initialDate: dataDate.isNotEmpty ? DateTime.parse(dataDate) : null,
        markedDates: analysisDays,
        markerColor: Colors.blue,
      );

      if (selectedDate != null) {
        LoggerUtil.i('选择的日期: $selectedDate');
        final selectedDateString = selectedDate.toString().split(" ")[0];
        _dataDate.value = selectedDateString;

        // 同时更新营养摄入数据和健康消耗数据
        await Future.wait([getTodayAnalysis(), getTodayHealthData(selectedDateString)]);

        LoggerUtil.i('日期切换完成，营养摄入和健康数据已更新');
      } else {
        LoggerUtil.i('取消选择日期');
      }
    });
  }

  /// 设置指定日期
  /// [date] 要设置的日期
  void setDate(String date) {
    _dataDate.value = date;
    // 同时更新营养摄入数据和健康消耗数据
    Future.wait([getTodayAnalysis(), getTodayHealthData(date)]);
  }

  /// 获取今天的日期字符串
  String getTodayDateString() {
    return DateTime.now().toString().split(" ")[0];
  }

  /// 切换到今天
  void switchToToday() {
    final todayString = getTodayDateString();
    LoggerUtil.i("切换到今天: $todayString");
    setDate(todayString);
  }

  /// 强制刷新今日数据（用于极光推送后的数据更新）
  Future<void> forceRefreshTodayData() async {
    LoggerUtil.d("forceRefreshTodayData - 强制刷新今日数据");

    // 确保切换到今天
    switchToToday();

    // 等待一小段时间确保数据库操作完成
    await Future.delayed(Duration(milliseconds: 100));

    // 强制重新查询营养数据和健康数据
    await Future.wait([getTodayAnalysis(), getTodayHealthData(dataDate)]);

    LoggerUtil.i("强制刷新完成，当前卡路里: $todayCalories");
  }

  /// 更新最后更新时间
  void _updateLastUpdateTime() {
    final now = DateTime.now();
    final timeString = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
    _lastUpdateTime.value = timeString;
    LoggerUtil.i("更新最后更新时间: $timeString");
  }

  /// 获取格式化的最后更新时间显示文本
  String getLastUpdateTimeText() {
    if (_lastUpdateTime.value.isEmpty) {
      return '暂无更新';
    }
    return '最后更新时间：${_lastUpdateTime.value}';
  }

  /// 获取健康数据
  /// 从苹果健康中获取指定日期的消耗数据
  /// [targetDate] 目标日期，如果为空则使用当前选择的日期
  Future<void> getTodayHealthData([String? targetDate]) async {
    await executeAsync(() async {
      try {
        // 使用传入的日期或当前选择的日期
        final dateToUse = targetDate ?? dataDate;
        LoggerUtil.i("开始获取健康数据 - 日期: $dateToUse");

        // 定义需要获取的健康数据类型
        final types = [
          HealthDataType.ACTIVE_ENERGY_BURNED, // 活动消耗的能量
          HealthDataType.BASAL_ENERGY_BURNED, // 基础代谢率
          HealthDataType.STEPS, // 步数
          HealthDataType.DISTANCE_WALKING_RUNNING, // 步行和跑步的距离
          HealthDataType.EXERCISE_TIME, // 运动时间
        ];

        // 请求权限
        bool hasAccess = await Health().requestAuthorization(types);

        if (!hasAccess) {
          LoggerUtil.w("健康数据权限被拒绝");
          _resetHealthData();
          return;
        }

        LoggerUtil.i("健康数据权限获取成功");

        // 解析目标日期
        final targetDateTime = DateTime.parse(dateToUse);
        final startOfDay = DateTime(targetDateTime.year, targetDateTime.month, targetDateTime.day);
        final endOfDay = DateTime(targetDateTime.year, targetDateTime.month, targetDateTime.day, 23, 59, 59);

        LoggerUtil.i("查询健康数据时间范围: $startOfDay 到 $endOfDay");

        final healthData = await Health().getHealthDataFromTypes(
          types: types,
          startTime: startOfDay,
          endTime: endOfDay,
        );

        LoggerUtil.i("获取到健康数据: ${healthData.length} 条记录");

        // 处理健康数据
        _processHealthData(healthData);
      } catch (e) {
        LoggerUtil.e('获取健康数据失败', e);
        _resetHealthData();
      }
    });
  }

  /// 处理健康数据
  /// 对获取到的健康数据进行分类和汇总
  void _processHealthData(List<HealthDataPoint> healthData) {
    double activeCalories = 0;
    double basalCalories = 0;
    int steps = 0;
    double distance = 0; // 米
    int exerciseTime = 0; // 秒

    // 清空并保存详细健康数据
    _detailedStepsData.clear();
    _detailedActiveCaloriesData.clear();
    _detailedBasalCaloriesData.clear();

    for (var data in healthData) {
      try {
        double value = 0;

        // 安全地获取数值
        if (data.value is NumericHealthValue) {
          value = (data.value as NumericHealthValue).numericValue.toDouble();
        } else {
          LoggerUtil.w('未知的健康数据值类型: ${data.value.runtimeType}');
          continue;
        }

        switch (data.type) {
          case HealthDataType.ACTIVE_ENERGY_BURNED:
            activeCalories += value;
            _detailedActiveCaloriesData.add(data); // 保存详细活动消耗数据
            LoggerUtil.d('活动消耗卡路里: $value kcal');
            break;
          case HealthDataType.BASAL_ENERGY_BURNED:
            basalCalories += value;
            _detailedBasalCaloriesData.add(data); // 保存详细基础代谢数据
            LoggerUtil.d('基础代谢卡路里: $value kcal');
            break;
          case HealthDataType.STEPS:
            steps += value.toInt();
            _detailedStepsData.add(data); // 保存详细步数数据
            LoggerUtil.d('步数: ${value.toInt()} 步');
            break;
          case HealthDataType.DISTANCE_WALKING_RUNNING:
            distance += value; // 单位是米
            LoggerUtil.d('距离: $value 米');
            break;
          case HealthDataType.EXERCISE_TIME:
            exerciseTime += value.toInt(); // 单位是秒
            LoggerUtil.d('运动时间: ${value.toInt()} 秒');
            break;
          default:
            break;
        }
      } catch (e) {
        LoggerUtil.e('处理健康数据时出错: ${data.type}', e);
      }
    }

    // 更新响应式变量
    _todayActiveCalories.value = activeCalories;
    _todayBasalCalories.value = basalCalories;
    _todayTotalBurnedCalories.value = activeCalories + basalCalories;
    _todaySteps.value = steps;
    _todayDistance.value = distance / 1000; // 转换为公里
    _todayExerciseTime.value = (exerciseTime / 60).round(); // 转换为分钟

    LoggerUtil.i("健康数据汇总完成:");
    LoggerUtil.i("活动消耗: ${_todayActiveCalories.value} kcal");
    LoggerUtil.i("基础代谢: ${_todayBasalCalories.value} kcal");
    LoggerUtil.i("总消耗: ${_todayTotalBurnedCalories.value} kcal");
    LoggerUtil.i("步数: ${_todaySteps.value} 步");
    LoggerUtil.i("距离: ${_todayDistance.value} 公里");
    LoggerUtil.i("运动时间: ${_todayExerciseTime.value} 分钟");
  }

  /// 重置健康数据
  void _resetHealthData() {
    _todayActiveCalories.value = 0.0;
    _todayBasalCalories.value = 0.0;
    _todayTotalBurnedCalories.value = 0.0;
    _todaySteps.value = 0;
    _todayDistance.value = 0.0;
    _todayExerciseTime.value = 0;
    _detailedStepsData.clear();
    _detailedActiveCaloriesData.clear();
    _detailedBasalCaloriesData.clear();
    LoggerUtil.i("健康数据已重置");
  }

  /// 显示步数详情弹窗
  void showStepsDetail() {
    if (_detailedStepsData.isEmpty) {
      Get.snackbar('提示', '暂无步数数据');
      return;
    }

    // 按时间排序步数数据
    final sortedData = List<HealthDataPoint>.from(_detailedStepsData);
    sortedData.sort((a, b) => a.dateFrom.compareTo(b.dateFrom));

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          width: Get.width * 0.9, // 使用屏幕宽度的90%
          height: Get.height * 0.8, // 使用屏幕高度的80%
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // 标题
              Row(
                children: [
                  Icon(Icons.directions_walk, color: Colors.purple[600], size: 24),
                  const SizedBox(width: 8),
                  const Expanded(child: Text('今日步数详情', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold))),
                  IconButton(onPressed: () => Get.back(), icon: const Icon(Icons.close, color: Colors.grey)),
                ],
              ),
              const SizedBox(height: 16),

              // 总步数统计
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(color: Colors.purple[50], borderRadius: BorderRadius.circular(12)),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('今日总步数', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
                    Text(
                      '$todaySteps 步',
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.purple[700]),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),

              // 折线图
              const Align(
                alignment: Alignment.centerLeft,
                child: Text('步数趋势图', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
              ),
              const SizedBox(height: 12),
              Container(
                height: 200,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: CaloriesDetailWidget.buildStepsChart(_detailedStepsData, Colors.purple[600]!),
              ),
              const SizedBox(height: 20),

              // 详细数据列表
              const Align(
                alignment: Alignment.centerLeft,
                child: Text('详细记录', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
              ),
              const SizedBox(height: 12),

              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey[200]!),
                  ),
                  child: CaloriesDetailWidget.buildStepsDataList(_detailedStepsData, Colors.purple[600]!),
                ),
              ),
            ],
          ),
        ),
      ),
      barrierDismissible: true,
    );
  }

  /// 显示总消耗详情弹窗
  void showTotalCaloriesDetail() {
    if (_detailedActiveCaloriesData.isEmpty && _detailedBasalCaloriesData.isEmpty) {
      Get.snackbar('提示', '暂无消耗数据');
      return;
    }

    Get.dialog(
      _buildCaloriesDetailDialog(
        title: '总消耗详情',
        icon: Icons.local_fire_department,
        color: Colors.orange,
        totalValue: todayTotalBurnedCalories.round(),
        activeData: _detailedActiveCaloriesData,
        basalData: _detailedBasalCaloriesData,
        showChart: true,
      ),
      barrierDismissible: true,
    );
  }

  /// 显示活动消耗详情弹窗
  void showActiveCaloriesDetail() {
    if (_detailedActiveCaloriesData.isEmpty) {
      Get.snackbar('提示', '暂无活动消耗数据');
      return;
    }

    Get.dialog(
      _buildCaloriesDetailDialog(
        title: '活动消耗详情',
        icon: Icons.directions_run,
        color: Colors.blue,
        totalValue: todayActiveCalories.round(),
        activeData: _detailedActiveCaloriesData,
        basalData: [],
        showChart: true,
      ),
      barrierDismissible: true,
    );
  }

  /// 显示基础代谢详情弹窗
  void showBasalCaloriesDetail() {
    if (_detailedBasalCaloriesData.isEmpty) {
      Get.snackbar('提示', '暂无基础代谢数据');
      return;
    }

    Get.dialog(
      _buildCaloriesDetailDialog(
        title: '基础代谢详情',
        icon: Icons.favorite,
        color: Colors.green,
        totalValue: todayBasalCalories.round(),
        activeData: [],
        basalData: _detailedBasalCaloriesData,
        showChart: true,
      ),
      barrierDismissible: true,
    );
  }

  /// 构建卡路里详情对话框
  Widget _buildCaloriesDetailDialog({
    required String title,
    required IconData icon,
    required Color color,
    required int totalValue,
    required List<HealthDataPoint> activeData,
    required List<HealthDataPoint> basalData,
    required bool showChart,
  }) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: Get.width * 0.9, // 使用屏幕宽度的90%
        height: Get.height * 0.8, // 使用屏幕高度的80%
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // 标题
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Expanded(child: Text(title, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold))),
                IconButton(onPressed: () => Get.back(), icon: const Icon(Icons.close, color: Colors.grey)),
              ],
            ),
            const SizedBox(height: 16),

            // 总消耗统计
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(color: color.withValues(alpha: 0.1), borderRadius: BorderRadius.circular(12)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(title.replaceAll('详情', ''), style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
                  Text('$totalValue kcal', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: color)),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // 折线图（如果有数据且需要显示）
            if (showChart && (activeData.isNotEmpty || basalData.isNotEmpty)) ...[
              const Align(
                alignment: Alignment.centerLeft,
                child: Text('消耗趋势图', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
              ),
              const SizedBox(height: 12),
              Container(
                height: 200,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: CaloriesDetailWidget.buildCaloriesChart(activeData, basalData, color),
              ),
              const SizedBox(height: 20),
            ],

            // 详细数据列表
            const Align(
              alignment: Alignment.centerLeft,
              child: Text('详细记录', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
            ),
            const SizedBox(height: 12),

            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: CaloriesDetailWidget.buildCaloriesDataList(activeData, basalData, color),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
