import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../../shared/widgets/cardIconTextVal/card_icon_text_val.dart';
import '../../../shared/widgets/cardIconTextValProgress/card_icon_text_val_progress.dart';
import '../../../shared/widgets/circularProgress/circular_progress.dart';
import '../../../routes/app_pages.dart';
import '../controllers/index_controller.dart';
import '../../../../core/constants/constants.dart';

/// 首页视图
class IndexView extends GetView<IndexController> {
  IndexView({super.key});

  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  @override
  Widget build(BuildContext context) {
    // 页面加载时获取今天的分析数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // _logger.i('页面加载时获取今天的分析数据');
      // controller.getTodayAnalysis();
    });
    return Scaffold(
      body: SafeArea(
        child: SmartRefresher(
          controller: _refreshController,
          enablePullDown: true,
          enablePullUp: false,
          header: CustomHeader(
            builder: (BuildContext context, RefreshStatus? mode) {
              Widget body;
              if (mode == RefreshStatus.idle) {
                body = const Text("下拉刷新", style: TextStyle(color: Colors.grey));
              } else if (mode == RefreshStatus.refreshing) {
                body = const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2)),
                    SizedBox(width: 8),
                    Text("正在同步数据...", style: TextStyle(color: Colors.grey)),
                  ],
                );
              } else if (mode == RefreshStatus.canRefresh) {
                body = Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text("松开立即同步数据", style: TextStyle(color: Colors.blue, fontWeight: FontWeight.w500)),
                    const SizedBox(height: 4),
                    Obx(
                      () => Text(
                        controller.getLastUpdateTimeText(),
                        style: const TextStyle(color: Colors.grey, fontSize: 12),
                      ),
                    ),
                  ],
                );
              } else {
                body = const Text("刷新完成", style: TextStyle(color: Colors.green));
              }
              return SizedBox(height: 60, child: Center(child: body));
            },
          ),
          onRefresh: _onRefresh,
          child: SingleChildScrollView(
            child: Column(
              children: [
                // _buildUserSection(),
                // _buildQuickActions(),
                _buildDailyBurnedOverview(),
                // LayoutConstants.spacingLarge,
                _buildDailyOverview(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _onRefresh() async {
    await controller.onRefresh();
    _refreshController.refreshCompleted();
  }

  Widget _buildDailyOverview() {
    return Builder(
      // 使用Builder获取context
      builder: (context) {
        return Padding(
          padding: LayoutConstants.defaultPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                    //跳转到饮食记录页面
                    onPressed: () => Get.toNamed(Routes.RECORD, arguments: {'date': controller.dataDate}),
                    child: Text(
                      '今日营养摄入',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Colors.grey[800]),
                    ),
                  ),
                  InkWell(
                    onTap: () => controller.selectDate(context),
                    child: Row(
                      children: [
                        Obx(
                          () => Text(
                            '${DateTime.parse(controller.dataDate).month}月${DateTime.parse(controller.dataDate).day}日',
                            style: TextStyle(fontSize: 14, color: Colors.blue[600]),
                          ),
                        ),
                        Icon(Icons.keyboard_arrow_down, size: 18, color: Colors.blue[600]),
                      ],
                    ),
                  ),
                ],
              ),
              LayoutConstants.spacingLarge,
              Container(
                padding: LayoutConstants.defaultPadding,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(LayoutConstants.borderRadiusLarge),
                  boxShadow: [
                    BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: Offset(0, 2)),
                  ],
                ),
                child: Column(
                  children: [
                    // 新增：圆形进度条和卡路里统计
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Obx(() {
                          return CircularProgress(
                            progress: controller.todayCalories / 2000,
                            consumed: controller.todayCalories, // 或根据组件实际类型调整
                            target: 2000,
                            onTap: () => {},
                          );
                        }),

                        LayoutConstants.spacingLarge,

                        Obx(() {
                          return Expanded(
                            child: Column(
                              children: [
                                CardIconTextVal(icon: Icons.flag, label: '目标摄入', value: 2000, onTap: () => {}),

                                LayoutConstants.spacingMedium,
                                CardIconTextVal(
                                  icon: Icons.local_fire_department,
                                  label: '已摄入',
                                  value: controller.todayCalories,
                                  onTap: () => {},
                                ),
                                LayoutConstants.spacingMedium,
                                CardIconTextVal(
                                  icon: Icons.compare_arrows,
                                  label: '剩余',
                                  value: 2000 - controller.todayCalories,
                                  onTap: () => {},
                                  isHighlight: true,
                                ),
                              ],
                            ),
                          );
                        }),
                      ],
                    ),

                    LayoutConstants.spacingExtraLarge,

                    // 营养元素网格改为更简洁的布局
                    Obx(
                      () => GridView.count(
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        crossAxisCount: 2,
                        childAspectRatio: 3,
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 8,
                        children: [
                          CardIconTextValProgress(
                            icon: Icons.fitness_center,
                            label: '蛋白质',
                            value: controller.todayProteinSum,
                            unit: 'g',
                            progress: 60,
                            color: Colors.blue,
                            onTap: () => {},
                          ),
                          CardIconTextValProgress(
                            icon: Icons.breakfast_dining,
                            label: '碳水',
                            unit: 'g',
                            value: controller.todayCarbsSum,
                            progress: 325,
                            color: Colors.orange,
                            onTap: () => {},
                          ),
                          CardIconTextValProgress(
                            icon: Icons.water_drop,
                            label: '脂肪',
                            value: controller.todayFatSum,
                            progress: 33,
                            unit: 'g',
                            color: Colors.red,
                            onTap: () => {},
                          ),
                          CardIconTextValProgress(
                            icon: Icons.medical_services,
                            label: '维生素C',
                            value: controller.todayVitaminCSum,
                            progress: 90,
                            unit: 'mg',
                            color: Colors.green,
                            onTap: () => {},
                          ),
                          CardIconTextValProgress(
                            icon: Icons.science,
                            label: '钠',
                            value: controller.todaySodiumSum,
                            progress: 1500,
                            unit: 'mg',
                            color: Colors.purple,
                            onTap: () => {},
                          ),
                          CardIconTextValProgress(
                            icon: Icons.grass,
                            label: '膳食纤维',
                            value: controller.todayFiberSum,
                            progress: 38,
                            unit: 'g',
                            color: Colors.teal,
                            onTap: () => {},
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建今日消耗数据概览
  Widget _buildDailyBurnedOverview() {
    return Builder(
      builder: (context) {
        return Padding(
          padding: LayoutConstants.defaultPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('今日消耗数据', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Colors.grey[800])),
                  Text('来自苹果健康', style: TextStyle(fontSize: 12, color: Colors.grey[500])),
                ],
              ),
              LayoutConstants.spacingLarge,
              Container(
                padding: LayoutConstants.defaultPadding,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(LayoutConstants.borderRadiusLarge),
                  boxShadow: [
                    BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: Offset(0, 2)),
                  ],
                ),
                child: Column(
                  children: [
                    // 简化的消耗数据展示 - 横向布局
                    Obx(() {
                      return Row(
                        children: [
                          // 总消耗卡路里
                          Expanded(
                            flex: 2,
                            child: GestureDetector(
                              onTap: () => controller.showTotalCaloriesDetail(),
                              child: Container(
                                padding: EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [Colors.orange[100]!, Colors.orange[50]!],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(color: Colors.orange[200]!, width: 1),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(Icons.local_fire_department, color: Colors.orange[600], size: 20),
                                        SizedBox(width: 6),
                                        Text('总消耗', style: TextStyle(fontSize: 14, color: Colors.grey[700])),
                                        Spacer(),
                                        Icon(Icons.info_outline, color: Colors.orange[400], size: 16),
                                      ],
                                    ),
                                    SizedBox(height: 8),
                                    Text(
                                      '${controller.todayTotalBurnedCalories.round()}',
                                      style: TextStyle(
                                        fontSize: 24,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.orange[700],
                                      ),
                                    ),
                                    Text('kcal', style: TextStyle(fontSize: 12, color: Colors.grey[600])),
                                  ],
                                ),
                              ),
                            ),
                          ),

                          SizedBox(width: 12),

                          // 活动消耗和基础代谢
                          Expanded(
                            flex: 3,
                            child: Column(
                              children: [
                                GestureDetector(
                                  onTap: () => controller.showActiveCaloriesDetail(),
                                  child: Container(
                                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                                    decoration: BoxDecoration(
                                      color: Colors.blue[50],
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(color: Colors.blue[100]!, width: 1),
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(Icons.directions_run, color: Colors.blue[600], size: 16),
                                        SizedBox(width: 8),
                                        Text('活动消耗', style: TextStyle(fontSize: 12, color: Colors.grey[700])),
                                        Spacer(),
                                        Icon(Icons.info_outline, color: Colors.blue[400], size: 14),
                                        SizedBox(width: 4),
                                        Text(
                                          '${controller.todayActiveCalories.round()} kcal',
                                          style: TextStyle(
                                            fontSize: 13,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.blue[700],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                SizedBox(height: 8),
                                GestureDetector(
                                  onTap: () => controller.showBasalCaloriesDetail(),
                                  child: Container(
                                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                                    decoration: BoxDecoration(
                                      color: Colors.green[50],
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(color: Colors.green[100]!, width: 1),
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(Icons.favorite, color: Colors.green[600], size: 16),
                                        SizedBox(width: 8),
                                        Text('基础代谢', style: TextStyle(fontSize: 12, color: Colors.grey[700])),
                                        Spacer(),
                                        Icon(Icons.info_outline, color: Colors.green[400], size: 14),
                                        SizedBox(width: 4),
                                        Text(
                                          '${controller.todayBasalCalories.round()} kcal',
                                          style: TextStyle(
                                            fontSize: 13,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.green[700],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
                    }),

                    LayoutConstants.spacingLarge,

                    // 运动数据 - 只保留步数和运动时间
                    Obx(
                      () => Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () => controller.showStepsDetail(),
                              child: Container(
                                padding: EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.purple[50],
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: Colors.purple[100]!, width: 1),
                                ),
                                child: Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(Icons.directions_walk, color: Colors.purple[600], size: 24),
                                        SizedBox(width: 4),
                                        Icon(Icons.info_outline, color: Colors.purple[400], size: 16),
                                      ],
                                    ),
                                    SizedBox(height: 6),
                                    Text(
                                      '${controller.todaySteps}',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.purple[700],
                                      ),
                                    ),
                                    Text('步', style: TextStyle(fontSize: 12, color: Colors.grey[600])),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: 12),
                          Expanded(
                            child: Container(
                              padding: EdgeInsets.all(12),
                              decoration: BoxDecoration(color: Colors.teal[50], borderRadius: BorderRadius.circular(8)),
                              child: Column(
                                children: [
                                  Icon(Icons.timer, color: Colors.teal[600], size: 24),
                                  SizedBox(height: 6),
                                  Text(
                                    '${controller.todayExerciseTime}',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.teal[700],
                                    ),
                                  ),
                                  Text('分钟', style: TextStyle(fontSize: 12, color: Colors.grey[600])),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

// 新增日期选择方法
// Future<void> _selectDate(BuildContext context) async {
//   final results = await showCalendarDatePicker2Dialog(
//     context: context,
//     config: CalendarDatePicker2WithActionButtonsConfig(
//       controlsHeight: 40, // 控制按钮的高度
//       controlsTextStyle: TextStyle(color: Colors.blue), // 控制按钮的文本样式(月份，年份)
//       // firstDate: DateTime(2024),
//       lastDate: DateTime.now(),
//       firstDayOfWeek: 1, // 周一为第一天
//       dayTextStyle: TextStyle(color: Colors.grey[700]), // 日期文本样式
//       calendarType: CalendarDatePicker2Type.single, // 日历类型为范围选择
//       selectedDayTextStyle: TextStyle(
//         color: Colors.white,
//         fontWeight: FontWeight.w700,
//       ), // 选中日期的文本样式
//       // selectedDayHighlightColor: Colors.blue, // 选中日期的背景颜色
//       // centerAlignModePicker: true, // 居中显示模式选择器
//       // customModePickerIcon: SizedBox(), // 自定义模式选择器图标
//       selectedDayHighlightColor: Colors.purple[800],
//     ),
//     dialogSize: const Size(325, 400), // 对话框大小

//     borderRadius: BorderRadius.circular(15.0),
//   );
// }
